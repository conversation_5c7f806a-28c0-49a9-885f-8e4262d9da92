import 'package:flutter/material.dart';
import '../../utils/constants.dart';
import '../../widgets/custom_text_field.dart';
import '../../widgets/video_card.dart';
import '../../widgets/custom_button.dart';

class PlaylistScreen extends StatefulWidget {
  const PlaylistScreen({super.key});

  @override
  State<PlaylistScreen> createState() => _PlaylistScreenState();
}

class _PlaylistScreenState extends State<PlaylistScreen> {
  final _searchController = TextEditingController();
  final _urlController = TextEditingController();
  
  @override
  void dispose() {
    _searchController.dispose();
    _urlController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            // App Bar
            Padding(
              padding: const EdgeInsets.all(AppConstants.paddingLarge),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        'My Playlist',
                        style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      IconButton(
                        icon: const Icon(Icons.add),
                        onPressed: _showAddVideoDialog,
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: AppConstants.paddingMedium),
                  
                  // Search Bar
                  SearchTextField(
                    controller: _searchController,
                    hintText: 'Search videos...',
                    onChanged: (value) {
                      // Implement search functionality
                    },
                  ),
                ],
              ),
            ),
            
            // Stats Row
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: AppConstants.paddingLarge),
              child: Row(
                children: [
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.all(AppConstants.paddingMedium),
                      decoration: BoxDecoration(
                        color: AppConstants.primaryColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
                      ),
                      child: Column(
                        children: [
                          Text(
                            '24',
                            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: AppConstants.primaryColor,
                            ),
                          ),
                          Text(
                            'Videos',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: AppConstants.primaryColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(width: AppConstants.paddingMedium),
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.all(AppConstants.paddingMedium),
                      decoration: BoxDecoration(
                        color: AppConstants.successColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
                      ),
                      child: Column(
                        children: [
                          Text(
                            '18h 45m',
                            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: AppConstants.successColor,
                            ),
                          ),
                          Text(
                            'Duration',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: AppConstants.successColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(width: AppConstants.paddingMedium),
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.all(AppConstants.paddingMedium),
                      decoration: BoxDecoration(
                        color: AppConstants.warningColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
                      ),
                      child: Column(
                        children: [
                          Text(
                            '75%',
                            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: AppConstants.warningColor,
                            ),
                          ),
                          Text(
                            'Watched',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: AppConstants.warningColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: AppConstants.paddingLarge),
            
            // Video List
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.symmetric(horizontal: AppConstants.paddingLarge),
                itemCount: 10, // Replace with actual video count
                itemBuilder: (context, index) {
                  return Padding(
                    padding: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
                    child: VideoCard(
                      title: 'Flutter Tutorial ${index + 1}: Building Beautiful UIs',
                      duration: '15:30',
                      thumbnailUrl: 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg',
                      progress: index % 3 == 0 ? 0.65 : index % 4 == 0 ? 1.0 : 0.0,
                      isWatched: index % 4 == 0,
                      onTap: () {
                        // Navigate to video player
                      },
                      onMoreOptions: () {
                        _showVideoOptionsBottomSheet(index);
                      },
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showAddVideoDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Video'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Enter a YouTube URL to add to your playlist',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppConstants.textSecondary,
              ),
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            CustomTextField(
              controller: _urlController,
              hintText: 'https://www.youtube.com/watch?v=...',
              prefixIcon: Icons.link,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a URL';
                }
                if (!value.contains('youtube.com') && !value.contains('youtu.be')) {
                  return 'Please enter a valid YouTube URL';
                }
                return null;
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _urlController.clear();
            },
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              // Add video to playlist
              Navigator.pop(context);
              _urlController.clear();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Video added to playlist!'),
                  backgroundColor: AppConstants.successColor,
                ),
              );
            },
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }

  void _showVideoOptionsBottomSheet(int index) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(AppConstants.radiusLarge),
            topRight: Radius.circular(AppConstants.radiusLarge),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle
            Container(
              margin: const EdgeInsets.only(top: AppConstants.paddingMedium),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            
            Padding(
              padding: const EdgeInsets.all(AppConstants.paddingLarge),
              child: Column(
                children: [
                  ListTile(
                    leading: const Icon(Icons.play_arrow),
                    title: const Text('Play Video'),
                    onTap: () {
                      Navigator.pop(context);
                      // Play video
                    },
                  ),
                  ListTile(
                    leading: const Icon(Icons.share),
                    title: const Text('Share'),
                    onTap: () {
                      Navigator.pop(context);
                      // Share video
                    },
                  ),
                  ListTile(
                    leading: const Icon(Icons.download),
                    title: const Text('Download'),
                    onTap: () {
                      Navigator.pop(context);
                      // Download video (if available)
                    },
                  ),
                  ListTile(
                    leading: const Icon(Icons.playlist_remove),
                    title: const Text('Remove from Playlist'),
                    onTap: () {
                      Navigator.pop(context);
                      // Remove from playlist
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Video removed from playlist'),
                          backgroundColor: AppConstants.warningColor,
                        ),
                      );
                    },
                  ),
                  ListTile(
                    leading: const Icon(Icons.report, color: AppConstants.errorColor),
                    title: const Text('Report', style: TextStyle(color: AppConstants.errorColor)),
                    onTap: () {
                      Navigator.pop(context);
                      // Report video
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
