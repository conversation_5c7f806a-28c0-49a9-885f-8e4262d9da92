import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../utils/constants.dart';
import '../../widgets/video_card.dart';

class VideoPlayerScreen extends StatefulWidget {
  final String videoId;
  final String title;
  final String description;
  final List<String> relatedVideoIds;

  const VideoPlayerScreen({
    super.key,
    required this.videoId,
    required this.title,
    this.description = '',
    this.relatedVideoIds = const [],
  });

  @override
  State<VideoPlayerScreen> createState() => _VideoPlayerScreenState();
}

class _VideoPlayerScreenState extends State<VideoPlayerScreen> {
  late WebViewController _webViewController;
  bool _isLoading = true;
  double _progress = 0.0;

  @override
  void initState() {
    super.initState();
    _initializeWebView();
  }

  void _initializeWebView() {
    _webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            setState(() {
              _isLoading = true;
            });
          },
          onPageFinished: (String url) {
            setState(() {
              _isLoading = false;
            });
          },
          onProgress: (int progress) {
            setState(() {
              _progress = progress / 100.0;
            });
          },
        ),
      )
      ..loadRequest(Uri.parse(_getEmbedUrl()));
  }

  String _getEmbedUrl() {
    return 'https://www.youtube.com/embed/${widget.videoId}?autoplay=1&rel=0&showinfo=0&modestbranding=1';
  }

  void _trackProgress() {
    // Here you would save progress to Firebase/local storage
    // Example: saveVideoProgress(widget.videoId, _progress);
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: AppBar(
        title: Text(widget.title),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.open_in_browser),
            onPressed: () async {
              final url = 'https://www.youtube.com/watch?v=${widget.videoId}';
              if (await canLaunchUrl(Uri.parse(url))) {
                await launchUrl(Uri.parse(url));
              }
            },
          ),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Video Player
            Container(
              height: 250,
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.black,
                borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
              ),
              margin: const EdgeInsets.all(AppConstants.paddingMedium),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
                child: Stack(
                  children: [
                    WebViewWidget(controller: _webViewController),
                    if (_isLoading)
                      Container(
                        color: Colors.black,
                        child: Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const CircularProgressIndicator(
                                color: AppConstants.primaryColor,
                              ),
                              const SizedBox(height: 16),
                              Text(
                                'Loading video...',
                                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  color: Colors.white,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
              
              // Video Info and Controls
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Video Title and Actions
                      Padding(
                        padding: const EdgeInsets.all(AppConstants.paddingMedium),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              widget.title,
                              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            
                            const SizedBox(height: AppConstants.paddingSmall),
                            
                            // Progress and Duration
                            if (!_isLoading) ...[
                              Row(
                                children: [
                                  Text(
                                    'Video loaded',
                                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                      color: AppConstants.textSecondary,
                                    ),
                                  ),
                                  const Spacer(),
                                  Text(
                                    '${(_progress * 100).toInt()}% loaded',
                                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                      color: AppConstants.primaryColor,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),

                              const SizedBox(height: AppConstants.paddingSmall),

                              LinearProgressIndicator(
                                value: _progress,
                                backgroundColor: AppConstants.primaryColor.withOpacity(0.1),
                                valueColor: const AlwaysStoppedAnimation<Color>(AppConstants.primaryColor),
                                borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
                              ),
                            ],
                            
                            const SizedBox(height: AppConstants.paddingMedium),
                            
                            // Action Buttons
                            Row(
                              children: [
                                _buildActionButton(
                                  icon: Icons.thumb_up_outlined,
                                  label: 'Like',
                                  onTap: () {
                                    // Handle like
                                  },
                                ),
                                const SizedBox(width: AppConstants.paddingMedium),
                                _buildActionButton(
                                  icon: Icons.share_outlined,
                                  label: 'Share',
                                  onTap: () {
                                    // Handle share
                                  },
                                ),
                                const SizedBox(width: AppConstants.paddingMedium),
                                _buildActionButton(
                                  icon: Icons.download_outlined,
                                  label: 'Download',
                                  onTap: () {
                                    // Handle download
                                  },
                                ),
                                const SizedBox(width: AppConstants.paddingMedium),
                                _buildActionButton(
                                  icon: Icons.playlist_add_outlined,
                                  label: 'Save',
                                  onTap: () {
                                    // Handle save to playlist
                                  },
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      
                      const Divider(),
                      
                      // Description
                      if (widget.description.isNotEmpty) ...[
                        Padding(
                          padding: const EdgeInsets.all(AppConstants.paddingMedium),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Description',
                                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              const SizedBox(height: AppConstants.paddingSmall),
                              Text(
                                widget.description,
                                style: Theme.of(context).textTheme.bodyMedium,
                              ),
                            ],
                          ),
                        ),
                        const Divider(),
                      ],
                      
                      // Related Videos
                      if (widget.relatedVideoIds.isNotEmpty) ...[
                        Padding(
                          padding: const EdgeInsets.all(AppConstants.paddingMedium),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Related Videos',
                                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              const SizedBox(height: AppConstants.paddingMedium),
                              ListView.builder(
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                itemCount: widget.relatedVideoIds.length,
                                itemBuilder: (context, index) {
                                  return Padding(
                                    padding: const EdgeInsets.only(bottom: AppConstants.paddingSmall),
                                    child: CompactVideoCard(
                                      title: 'Related Video ${index + 1}',
                                      duration: '10:30',
                                      thumbnailUrl: 'https://img.youtube.com/vi/${widget.relatedVideoIds[index]}/maxresdefault.jpg',
                                      onTap: () {
                                        Navigator.pushReplacement(
                                          context,
                                          MaterialPageRoute(
                                            builder: (context) => VideoPlayerScreen(
                                              videoId: widget.relatedVideoIds[index],
                                              title: 'Related Video ${index + 1}',
                                              relatedVideoIds: widget.relatedVideoIds,
                                            ),
                                          ),
                                        );
                                      },
                                    ),
                                  );
                                },
                              ),
                            ],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(AppConstants.paddingSmall),
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
            ),
            child: Icon(
              icon,
              color: AppConstants.textSecondary,
              size: 20,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppConstants.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  void _openInYouTube() async {
    final url = 'https://www.youtube.com/watch?v=${widget.videoId}';
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url));
    }
  }

  void _markVideoCompleted() {
    // Mark video as completed in database
    // Example: updateVideoProgress(widget.videoId, 1.0, true);
  }

  void _showNextVideoDialog() {
    if (widget.relatedVideoIds.isNotEmpty) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Video Completed!'),
          content: const Text('Would you like to watch the next video?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('No, thanks'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                Navigator.pushReplacement(
                  context,
                  MaterialPageRoute(
                    builder: (context) => VideoPlayerScreen(
                      videoId: widget.relatedVideoIds.first,
                      title: 'Next Video',
                      relatedVideoIds: widget.relatedVideoIds,
                    ),
                  ),
                );
              },
              child: const Text('Play Next'),
            ),
          ],
        ),
      );
    }
  }
}
