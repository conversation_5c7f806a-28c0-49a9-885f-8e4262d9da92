# Spirit App - Modern Mobile Learning Platform

A beautiful, intuitive mobile learning app built with Flutter that allows users to create playlists from YouTube videos and track their learning progress.

## Features

### 🎯 Core Features
- **Clean Authentication**: Firebase-powered login/signup with Google Sign-In
- **Modern Dashboard**: Personalized home screen with progress tracking
- **Video Playlists**: Add YouTube videos to custom playlists
- **Progress Tracking**: Monitor learning progress with detailed analytics
- **Course Management**: Organize videos into structured courses
- **Video Player**: Integrated YouTube player with progress tracking
- **Notifications**: Push-style alerts for new content and reminders

### 🎨 Design Features
- **Modern UI**: Clean, minimalist design with soft shadows and rounded corners
- **Typography**: Beautiful Inter font family throughout the app
- **Color Palette**: Calming blue-violet and indigo color scheme
- **Responsive**: Optimized for Android devices
- **Smooth Animations**: Fluid transitions and micro-interactions

### 🔧 Technical Features
- **Firebase Integration**: Authentication, Firestore database
- **YouTube Integration**: Video embedding and metadata extraction
- **State Management**: Provider pattern for reactive UI
- **Offline Support**: Local storage for user preferences
- **Progress Persistence**: Automatic saving of video watch progress

## Screenshots

[Add screenshots here when available]

## Getting Started

### Prerequisites
- Flutter SDK (3.8.0 or higher)
- Android Studio / VS Code
- Firebase project setup
- YouTube Data API key

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd spirit
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Firebase Setup**
   - Create a new Firebase project at [Firebase Console](https://console.firebase.google.com/)
   - Enable Authentication (Email/Password and Google Sign-In)
   - Enable Firestore Database
   - Download `google-services.json` and place it in `android/app/`
   - Update the configuration in the file with your actual Firebase project details

4. **YouTube API Setup**
   - Get a YouTube Data API key from [Google Cloud Console](https://console.cloud.google.com/)
   - Update `lib/utils/constants.dart` with your API key:
     ```dart
     static const String youtubeApiKey = 'YOUR_ACTUAL_API_KEY';
     ```

5. **Run the app**
   ```bash
   flutter run
   ```

## Project Structure

```
lib/
├── main.dart                 # App entry point
├── models/                   # Data models
│   ├── user_model.dart
│   ├── video_model.dart
│   └── course_model.dart
├── screens/                  # UI screens
│   ├── auth/                # Authentication screens
│   ├── home/                # Home dashboard
│   ├── courses/             # Course management
│   ├── playlist/            # Video playlists
│   ├── video/               # Video player
│   ├── profile/             # User profile
│   └── notifications/       # Notification center
├── services/                # Business logic
│   └── auth_service.dart    # Firebase authentication
├── widgets/                 # Reusable UI components
│   ├── custom_button.dart
│   ├── custom_text_field.dart
│   ├── course_card.dart
│   ├── video_card.dart
│   └── progress_card.dart
└── utils/                   # Utilities and constants
    ├── constants.dart       # App constants and colors
    └── theme.dart          # App theme configuration
```

## Key Dependencies

- **firebase_core**: Firebase SDK core
- **firebase_auth**: Authentication
- **cloud_firestore**: Database
- **google_sign_in**: Google authentication
- **youtube_player_flutter**: YouTube video player
- **provider**: State management
- **google_fonts**: Typography
- **cached_network_image**: Image caching
- **http**: API requests

## Configuration

### Firebase Configuration
1. Replace the placeholder `google-services.json` with your actual Firebase configuration
2. Update package name in `android/app/build.gradle.kts` if needed
3. Configure authentication providers in Firebase Console

### YouTube API Configuration
1. Enable YouTube Data API v3 in Google Cloud Console
2. Create credentials (API key)
3. Update the API key in `lib/utils/constants.dart`

## Usage

### Adding Videos to Playlist
1. Navigate to the Playlist tab
2. Tap the "+" button
3. Enter a YouTube URL
4. The video will be added to your playlist

### Tracking Progress
- Video progress is automatically tracked while watching
- Course completion is calculated based on video progress
- View detailed analytics in the Profile section

### Course Management
- Organize videos into courses
- Set learning milestones
- Track completion certificates

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

For support, email <EMAIL> or create an issue in this repository.

## Acknowledgments

- Flutter team for the amazing framework
- Firebase for backend services
- YouTube for video platform integration
- Google Fonts for beautiful typography
