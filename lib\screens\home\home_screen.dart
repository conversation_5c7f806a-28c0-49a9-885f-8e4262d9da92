import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:provider/provider.dart';
import '../../utils/constants.dart';
import '../../widgets/custom_text_field.dart';
import '../../widgets/course_card.dart';
import '../../widgets/progress_card.dart';
import '../../widgets/category_chip.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final _searchController = TextEditingController();
  String _selectedCategory = 'All';

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  String _getGreeting() {
    final hour = DateTime.now().hour;
    if (hour < 12) {
      return 'Good Morning';
    } else if (hour < 17) {
      return 'Good Afternoon';
    } else {
      return 'Good Evening';
    }
  }

  @override
  Widget build(BuildContext context) {
    final user = Provider.of<User?>(context);
    final userName = user?.displayName?.split(' ').first ?? 'Learner';
    
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      body: SafeArea(
        child: CustomScrollView(
          slivers: [
            // App Bar
            SliverAppBar(
              backgroundColor: Colors.transparent,
              elevation: 0,
              floating: true,
              snap: true,
              title: Row(
                children: [
                  Container(
                    decoration: BoxDecoration(
                      gradient: AppConstants.primaryGradient,
                      shape: BoxShape.circle,
                    ),
                    padding: const EdgeInsets.all(8),
                    child: const Icon(
                      Icons.school_rounded,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: AppConstants.paddingMedium),
                  Text(
                    AppConstants.appName,
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppConstants.textPrimary,
                    ),
                  ),
                ],
              ),
              actions: [
                IconButton(
                  icon: const Icon(
                    Icons.notifications_outlined,
                    color: AppConstants.textPrimary,
                  ),
                  onPressed: () {
                    // Navigate to notifications
                  },
                ),
                const SizedBox(width: AppConstants.paddingSmall),
              ],
            ),
            
            // Content
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.all(AppConstants.paddingLarge),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Welcome Message
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(AppConstants.paddingLarge),
                      decoration: BoxDecoration(
                        gradient: AppConstants.primaryGradient,
                        borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
                        boxShadow: AppConstants.mediumShadow,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '${_getGreeting()}, $userName! 👋',
                            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: AppConstants.paddingSmall),
                          Text(
                            'Ready to continue your learning journey?',
                            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                              color: Colors.white.withOpacity(0.9),
                            ),
                          ),
                        ],
                      ),
                    ),
                    
                    const SizedBox(height: AppConstants.paddingLarge),
                    
                    // Search Bar
                    SearchTextField(
                      controller: _searchController,
                      hintText: 'Search courses, videos...',
                      onChanged: (value) {
                        // Implement search functionality
                      },
                    ),
                    
                    const SizedBox(height: AppConstants.paddingLarge),
                    
                    // Progress Overview
                    Text(
                      'Your Progress',
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    
                    const SizedBox(height: AppConstants.paddingMedium),
                    
                    const Row(
                      children: [
                        Expanded(
                          child: ProgressCard(
                            title: 'Courses Completed',
                            value: '12',
                            icon: Icons.school,
                            color: AppConstants.successColor,
                          ),
                        ),
                        SizedBox(width: AppConstants.paddingMedium),
                        Expanded(
                          child: ProgressCard(
                            title: 'Hours Watched',
                            value: '48h',
                            icon: Icons.play_circle,
                            color: AppConstants.primaryColor,
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: AppConstants.paddingMedium),
                    
                    const Row(
                      children: [
                        Expanded(
                          child: ProgressCard(
                            title: 'Current Streak',
                            value: '7 days',
                            icon: Icons.local_fire_department,
                            color: AppConstants.warningColor,
                          ),
                        ),
                        SizedBox(width: AppConstants.paddingMedium),
                        Expanded(
                          child: ProgressCard(
                            title: 'Certificates',
                            value: '3',
                            icon: Icons.workspace_premium,
                            color: AppConstants.accentColor,
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: AppConstants.paddingLarge),
                    
                    // Categories
                    Text(
                      'Categories',
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    
                    const SizedBox(height: AppConstants.paddingMedium),
                    
                    SizedBox(
                      height: 40,
                      child: ListView.builder(
                        scrollDirection: Axis.horizontal,
                        itemCount: ['All', ...AppConstants.courseCategories].length,
                        itemBuilder: (context, index) {
                          final category = ['All', ...AppConstants.courseCategories][index];
                          return Padding(
                            padding: EdgeInsets.only(
                              right: AppConstants.paddingMedium,
                              left: index == 0 ? 0 : 0,
                            ),
                            child: CategoryChip(
                              label: category,
                              isSelected: _selectedCategory == category,
                              onTap: () {
                                setState(() {
                                  _selectedCategory = category;
                                });
                              },
                            ),
                          );
                        },
                      ),
                    ),
                    
                    const SizedBox(height: AppConstants.paddingLarge),
                    
                    // Featured Courses
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Featured Courses',
                          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        TextButton(
                          onPressed: () {
                            // Navigate to all courses
                          },
                          child: const Text('See All'),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: AppConstants.paddingMedium),
                    
                    // Course Cards
                    SizedBox(
                      height: 280,
                      child: ListView.builder(
                        scrollDirection: Axis.horizontal,
                        itemCount: 5, // Replace with actual course count
                        itemBuilder: (context, index) {
                          return Padding(
                            padding: EdgeInsets.only(
                              right: AppConstants.paddingMedium,
                              left: index == 0 ? 0 : 0,
                            ),
                            child: SizedBox(
                              width: 250,
                              child: CourseCard(
                                title: 'Flutter Development Masterclass',
                                instructor: 'John Doe',
                                duration: '12h 30m',
                                rating: 4.8,
                                enrollmentCount: '2.5K',
                                thumbnailUrl: 'https://via.placeholder.com/250x140',
                                onTap: () {
                                  // Navigate to course details
                                },
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                    
                    const SizedBox(height: AppConstants.paddingLarge),
                    
                    // Continue Learning
                    Text(
                      'Continue Learning',
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    
                    const SizedBox(height: AppConstants.paddingMedium),
                    
                    // Continue Learning Cards
                    ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: 3, // Replace with actual count
                      itemBuilder: (context, index) {
                        return Padding(
                          padding: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
                          child: CourseCard(
                            title: 'React Native Complete Guide',
                            instructor: 'Jane Smith',
                            duration: '8h 45m',
                            rating: 4.6,
                            enrollmentCount: '1.8K',
                            thumbnailUrl: 'https://via.placeholder.com/120x80',
                            progress: 0.65,
                            isHorizontal: true,
                            onTap: () {
                              // Navigate to course details
                            },
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
