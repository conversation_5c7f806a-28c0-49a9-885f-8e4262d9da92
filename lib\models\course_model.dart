import 'package:flutter/material.dart';
import 'video_model.dart';

class CourseModel {
  final String id;
  final String title;
  final String description;
  final String thumbnailUrl;
  final String category;
  final String instructor;
  final String instructorPhotoUrl;
  final List<String> videoIds;
  final List<VideoModel> videos;
  final Duration totalDuration;
  final String difficulty; // Beginner, Intermediate, Advanced
  final List<String> tags;
  final DateTime createdAt;
  final DateTime updatedAt;
  final double rating;
  final int enrollmentCount;
  final bool isFree;
  final double price;
  final List<String> prerequisites;
  final List<String> learningOutcomes;
  final Map<String, dynamic> metadata;

  CourseModel({
    required this.id,
    required this.title,
    required this.description,
    required this.thumbnailUrl,
    required this.category,
    required this.instructor,
    this.instructorPhotoUrl = '',
    this.videoIds = const [],
    this.videos = const [],
    required this.totalDuration,
    this.difficulty = 'Beginner',
    this.tags = const [],
    required this.createdAt,
    required this.updatedAt,
    this.rating = 0.0,
    this.enrollmentCount = 0,
    this.isFree = true,
    this.price = 0.0,
    this.prerequisites = const [],
    this.learningOutcomes = const [],
    this.metadata = const {},
  });

  factory CourseModel.fromJson(Map<String, dynamic> json) {
    return CourseModel(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      thumbnailUrl: json['thumbnailUrl'] ?? '',
      category: json['category'] ?? '',
      instructor: json['instructor'] ?? '',
      instructorPhotoUrl: json['instructorPhotoUrl'] ?? '',
      videoIds: List<String>.from(json['videoIds'] ?? []),
      videos: (json['videos'] as List<dynamic>?)
          ?.map((video) => VideoModel.fromJson(video))
          .toList() ?? [],
      totalDuration: Duration(seconds: json['totalDurationSeconds'] ?? 0),
      difficulty: json['difficulty'] ?? 'Beginner',
      tags: List<String>.from(json['tags'] ?? []),
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updatedAt'] ?? DateTime.now().toIso8601String()),
      rating: (json['rating'] ?? 0.0).toDouble(),
      enrollmentCount: json['enrollmentCount'] ?? 0,
      isFree: json['isFree'] ?? true,
      price: (json['price'] ?? 0.0).toDouble(),
      prerequisites: List<String>.from(json['prerequisites'] ?? []),
      learningOutcomes: List<String>.from(json['learningOutcomes'] ?? []),
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'thumbnailUrl': thumbnailUrl,
      'category': category,
      'instructor': instructor,
      'instructorPhotoUrl': instructorPhotoUrl,
      'videoIds': videoIds,
      'videos': videos.map((video) => video.toJson()).toList(),
      'totalDurationSeconds': totalDuration.inSeconds,
      'difficulty': difficulty,
      'tags': tags,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'rating': rating,
      'enrollmentCount': enrollmentCount,
      'isFree': isFree,
      'price': price,
      'prerequisites': prerequisites,
      'learningOutcomes': learningOutcomes,
      'metadata': metadata,
    };
  }

  CourseModel copyWith({
    String? id,
    String? title,
    String? description,
    String? thumbnailUrl,
    String? category,
    String? instructor,
    String? instructorPhotoUrl,
    List<String>? videoIds,
    List<VideoModel>? videos,
    Duration? totalDuration,
    String? difficulty,
    List<String>? tags,
    DateTime? createdAt,
    DateTime? updatedAt,
    double? rating,
    int? enrollmentCount,
    bool? isFree,
    double? price,
    List<String>? prerequisites,
    List<String>? learningOutcomes,
    Map<String, dynamic>? metadata,
  }) {
    return CourseModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      category: category ?? this.category,
      instructor: instructor ?? this.instructor,
      instructorPhotoUrl: instructorPhotoUrl ?? this.instructorPhotoUrl,
      videoIds: videoIds ?? this.videoIds,
      videos: videos ?? this.videos,
      totalDuration: totalDuration ?? this.totalDuration,
      difficulty: difficulty ?? this.difficulty,
      tags: tags ?? this.tags,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      rating: rating ?? this.rating,
      enrollmentCount: enrollmentCount ?? this.enrollmentCount,
      isFree: isFree ?? this.isFree,
      price: price ?? this.price,
      prerequisites: prerequisites ?? this.prerequisites,
      learningOutcomes: learningOutcomes ?? this.learningOutcomes,
      metadata: metadata ?? this.metadata,
    );
  }

  // Helper methods
  String getFormattedDuration() {
    int hours = totalDuration.inHours;
    int minutes = totalDuration.inMinutes.remainder(60);

    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }

  String getFormattedRating() {
    return rating.toStringAsFixed(1);
  }

  String getFormattedEnrollmentCount() {
    if (enrollmentCount < 1000) {
      return enrollmentCount.toString();
    } else if (enrollmentCount < 1000000) {
      return '${(enrollmentCount / 1000).toStringAsFixed(1)}K';
    } else {
      return '${(enrollmentCount / 1000000).toStringAsFixed(1)}M';
    }
  }

  String getFormattedPrice() {
    if (isFree) return 'Free';
    return '\$${price.toStringAsFixed(2)}';
  }

  int get videoCount => videos.isNotEmpty ? videos.length : videoIds.length;

  bool hasTag(String tag) {
    return tags.contains(tag.toLowerCase());
  }

  Color getDifficultyColor() {
    switch (difficulty.toLowerCase()) {
      case 'beginner':
        return const Color(0xFF10B981); // Green
      case 'intermediate':
        return const Color(0xFFF59E0B); // Orange
      case 'advanced':
        return const Color(0xFFEF4444); // Red
      default:
        return const Color(0xFF6B7280); // Gray
    }
  }
}

class CourseProgress {
  final String courseId;
  final String userId;
  final int completedVideos;
  final int totalVideos;
  final Duration totalWatchTime;
  final DateTime enrolledAt;
  final DateTime? completedAt;
  final Map<String, double> videoProgress; // videoId -> progress (0.0 to 1.0)
  final List<String> milestones;

  CourseProgress({
    required this.courseId,
    required this.userId,
    this.completedVideos = 0,
    required this.totalVideos,
    this.totalWatchTime = Duration.zero,
    required this.enrolledAt,
    this.completedAt,
    this.videoProgress = const {},
    this.milestones = const [],
  });

  factory CourseProgress.fromJson(Map<String, dynamic> json) {
    return CourseProgress(
      courseId: json['courseId'] ?? '',
      userId: json['userId'] ?? '',
      completedVideos: json['completedVideos'] ?? 0,
      totalVideos: json['totalVideos'] ?? 0,
      totalWatchTime: Duration(seconds: json['totalWatchTimeSeconds'] ?? 0),
      enrolledAt: DateTime.parse(json['enrolledAt'] ?? DateTime.now().toIso8601String()),
      completedAt: json['completedAt'] != null ? DateTime.parse(json['completedAt']) : null,
      videoProgress: Map<String, double>.from(json['videoProgress'] ?? {}),
      milestones: List<String>.from(json['milestones'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'courseId': courseId,
      'userId': userId,
      'completedVideos': completedVideos,
      'totalVideos': totalVideos,
      'totalWatchTimeSeconds': totalWatchTime.inSeconds,
      'enrolledAt': enrolledAt.toIso8601String(),
      'completedAt': completedAt?.toIso8601String(),
      'videoProgress': videoProgress,
      'milestones': milestones,
    };
  }

  double get progressPercentage {
    if (totalVideos == 0) return 0.0;
    return (completedVideos / totalVideos).clamp(0.0, 1.0);
  }

  String get formattedProgress {
    return '${(progressPercentage * 100).toInt()}%';
  }

  bool get isCompleted => completedAt != null;

  String getFormattedWatchTime() {
    int hours = totalWatchTime.inHours;
    int minutes = totalWatchTime.inMinutes.remainder(60);

    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }
}
