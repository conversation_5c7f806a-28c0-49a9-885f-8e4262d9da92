import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../utils/constants.dart';

class CourseCard extends StatelessWidget {
  final String title;
  final String instructor;
  final String duration;
  final double rating;
  final String enrollmentCount;
  final String thumbnailUrl;
  final double? progress;
  final bool isHorizontal;
  final VoidCallback? onTap;
  final VoidCallback? onFavorite;
  final bool isFavorite;

  const CourseCard({
    super.key,
    required this.title,
    required this.instructor,
    required this.duration,
    required this.rating,
    required this.enrollmentCount,
    required this.thumbnailUrl,
    this.progress,
    this.isHorizontal = false,
    this.onTap,
    this.onFavorite,
    this.isFavorite = false,
  });

  @override
  Widget build(BuildContext context) {
    if (isHorizontal) {
      return _buildHorizontalCard(context);
    } else {
      return _buildVerticalCard(context);
    }
  }

  Widget _buildVerticalCard(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: AppConstants.cardColor,
          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
          boxShadow: AppConstants.softShadow,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Thumbnail
            Stack(
              children: [
                ClipRRect(
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(AppConstants.radiusMedium),
                    topRight: Radius.circular(AppConstants.radiusMedium),
                  ),
                  child: CachedNetworkImage(
                    imageUrl: thumbnailUrl,
                    height: 140,
                    width: double.infinity,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Container(
                      height: 140,
                      color: Colors.grey.shade200,
                      child: const Center(
                        child: CircularProgressIndicator(),
                      ),
                    ),
                    errorWidget: (context, url, error) => Container(
                      height: 140,
                      color: Colors.grey.shade200,
                      child: const Icon(
                        Icons.image_not_supported,
                        color: AppConstants.textSecondary,
                      ),
                    ),
                  ),
                ),
                // Favorite Button
                if (onFavorite != null)
                  Positioned(
                    top: AppConstants.paddingSmall,
                    right: AppConstants.paddingSmall,
                    child: GestureDetector(
                      onTap: onFavorite,
                      child: Container(
                        padding: const EdgeInsets.all(6),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.9),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          isFavorite ? Icons.favorite : Icons.favorite_border,
                          color: isFavorite ? AppConstants.errorColor : AppConstants.textSecondary,
                          size: 16,
                        ),
                      ),
                    ),
                  ),
                // Duration Badge
                Positioned(
                  bottom: AppConstants.paddingSmall,
                  right: AppConstants.paddingSmall,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppConstants.paddingSmall,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.7),
                      borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
                    ),
                    child: Text(
                      duration,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.white,
                        fontSize: 10,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            
            // Content
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(AppConstants.paddingMedium),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Title
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    
                    const SizedBox(height: AppConstants.paddingSmall),
                    
                    // Instructor
                    Text(
                      instructor,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppConstants.textSecondary,
                      ),
                    ),
                    
                    const Spacer(),
                    
                    // Progress Bar (if available)
                    if (progress != null) ...[
                      LinearProgressIndicator(
                        value: progress,
                        backgroundColor: AppConstants.primaryColor.withOpacity(0.1),
                        valueColor: const AlwaysStoppedAnimation<Color>(AppConstants.primaryColor),
                        borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
                      ),
                      const SizedBox(height: AppConstants.paddingSmall),
                      Text(
                        '${(progress! * 100).toInt()}% Complete',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppConstants.primaryColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ] else ...[
                      // Rating and Enrollment
                      Row(
                        children: [
                          Icon(
                            Icons.star,
                            color: AppConstants.warningColor,
                            size: 14,
                          ),
                          const SizedBox(width: 2),
                          Text(
                            rating.toString(),
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(width: AppConstants.paddingSmall),
                          Text(
                            '($enrollmentCount)',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: AppConstants.textSecondary,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHorizontalCard(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        decoration: BoxDecoration(
          color: AppConstants.cardColor,
          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
          boxShadow: AppConstants.softShadow,
        ),
        child: Row(
          children: [
            // Thumbnail
            Stack(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
                  child: CachedNetworkImage(
                    imageUrl: thumbnailUrl,
                    height: 80,
                    width: 120,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Container(
                      height: 80,
                      width: 120,
                      color: Colors.grey.shade200,
                      child: const Center(
                        child: CircularProgressIndicator(),
                      ),
                    ),
                    errorWidget: (context, url, error) => Container(
                      height: 80,
                      width: 120,
                      color: Colors.grey.shade200,
                      child: const Icon(
                        Icons.image_not_supported,
                        color: AppConstants.textSecondary,
                      ),
                    ),
                  ),
                ),
                // Duration Badge
                Positioned(
                  bottom: 4,
                  right: 4,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 6,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.7),
                      borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
                    ),
                    child: Text(
                      duration,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.white,
                        fontSize: 10,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(width: AppConstants.paddingMedium),
            
            // Content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title
                  Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  
                  const SizedBox(height: AppConstants.paddingSmall),
                  
                  // Instructor
                  Text(
                    instructor,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppConstants.textSecondary,
                    ),
                  ),
                  
                  const SizedBox(height: AppConstants.paddingSmall),
                  
                  // Progress Bar (if available)
                  if (progress != null) ...[
                    LinearProgressIndicator(
                      value: progress,
                      backgroundColor: AppConstants.primaryColor.withOpacity(0.1),
                      valueColor: const AlwaysStoppedAnimation<Color>(AppConstants.primaryColor),
                      borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${(progress! * 100).toInt()}% Complete',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppConstants.primaryColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ] else ...[
                    // Rating and Enrollment
                    Row(
                      children: [
                        Icon(
                          Icons.star,
                          color: AppConstants.warningColor,
                          size: 14,
                        ),
                        const SizedBox(width: 2),
                        Text(
                          rating.toString(),
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(width: AppConstants.paddingSmall),
                        Text(
                          '($enrollmentCount)',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppConstants.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            ),
            
            // Favorite Button
            if (onFavorite != null)
              GestureDetector(
                onTap: onFavorite,
                child: Container(
                  padding: const EdgeInsets.all(8),
                  child: Icon(
                    isFavorite ? Icons.favorite : Icons.favorite_border,
                    color: isFavorite ? AppConstants.errorColor : AppConstants.textSecondary,
                    size: 20,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
