import 'package:flutter/material.dart';
import '../../utils/constants.dart';

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({super.key});

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  
  final List<String> _tabs = ['All', 'Course Updates', 'Reminders', 'Achievements'];
  
  final List<NotificationItem> _notifications = [
    NotificationItem(
      id: '1',
      title: 'New video available!',
      message: 'Flutter Advanced Concepts - Part 5 is now available in your course.',
      type: NotificationType.courseUpdate,
      timestamp: DateTime.now().subtract(const Duration(minutes: 30)),
      isRead: false,
      icon: Icons.play_circle_filled,
      color: AppConstants.primaryColor,
    ),
    NotificationItem(
      id: '2',
      title: 'Daily learning reminder',
      message: 'Don\'t forget to continue your learning streak! You have 2 videos pending.',
      type: NotificationType.reminder,
      timestamp: DateTime.now().subtract(const Duration(hours: 2)),
      isRead: false,
      icon: Icons.schedule,
      color: AppConstants.warningColor,
    ),
    NotificationItem(
      id: '3',
      title: 'Achievement unlocked! 🎉',
      message: 'Congratulations! You\'ve completed 10 courses and earned the "Dedicated Learner" badge.',
      type: NotificationType.achievement,
      timestamp: DateTime.now().subtract(const Duration(hours: 5)),
      isRead: true,
      icon: Icons.emoji_events,
      color: AppConstants.successColor,
    ),
    NotificationItem(
      id: '4',
      title: 'Course completed!',
      message: 'You\'ve successfully completed "React Native Fundamentals". Your certificate is ready!',
      type: NotificationType.courseUpdate,
      timestamp: DateTime.now().subtract(const Duration(days: 1)),
      isRead: true,
      icon: Icons.school,
      color: AppConstants.successColor,
    ),
    NotificationItem(
      id: '5',
      title: 'Weekly progress summary',
      message: 'This week you watched 5 hours of content and completed 3 courses. Keep it up!',
      type: NotificationType.reminder,
      timestamp: DateTime.now().subtract(const Duration(days: 2)),
      isRead: true,
      icon: Icons.trending_up,
      color: AppConstants.accentColor,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabs.length, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  List<NotificationItem> _getFilteredNotifications(int tabIndex) {
    switch (tabIndex) {
      case 0: // All
        return _notifications;
      case 1: // Course Updates
        return _notifications.where((n) => n.type == NotificationType.courseUpdate).toList();
      case 2: // Reminders
        return _notifications.where((n) => n.type == NotificationType.reminder).toList();
      case 3: // Achievements
        return _notifications.where((n) => n.type == NotificationType.achievement).toList();
      default:
        return _notifications;
    }
  }

  @override
  Widget build(BuildContext context) {
    final unreadCount = _notifications.where((n) => !n.isRead).length;
    
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: AppBar(
        title: Row(
          children: [
            const Text('Notifications'),
            if (unreadCount > 0) ...[
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: AppConstants.errorColor,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  unreadCount.toString(),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ],
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          if (unreadCount > 0)
            TextButton(
              onPressed: _markAllAsRead,
              child: const Text('Mark all read'),
            ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              // Navigate to notification settings
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Tabs
          Container(
            margin: const EdgeInsets.all(AppConstants.paddingMedium),
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
            ),
            child: TabBar(
              controller: _tabController,
              indicator: BoxDecoration(
                color: AppConstants.primaryColor,
                borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
              ),
              labelColor: Colors.white,
              unselectedLabelColor: AppConstants.textSecondary,
              labelStyle: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
              unselectedLabelStyle: Theme.of(context).textTheme.bodySmall,
              isScrollable: true,
              tabs: _tabs.map((tab) => Tab(text: tab)).toList(),
            ),
          ),
          
          // Notification List
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: List.generate(_tabs.length, (index) {
                final filteredNotifications = _getFilteredNotifications(index);
                
                if (filteredNotifications.isEmpty) {
                  return _buildEmptyState();
                }
                
                return ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: AppConstants.paddingMedium),
                  itemCount: filteredNotifications.length,
                  itemBuilder: (context, itemIndex) {
                    final notification = filteredNotifications[itemIndex];
                    return Padding(
                      padding: const EdgeInsets.only(bottom: AppConstants.paddingSmall),
                      child: _buildNotificationCard(notification),
                    );
                  },
                );
              }),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationCard(NotificationItem notification) {
    return GestureDetector(
      onTap: () => _markAsRead(notification),
      child: Container(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        decoration: BoxDecoration(
          color: notification.isRead 
              ? AppConstants.cardColor 
              : AppConstants.primaryColor.withOpacity(0.05),
          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
          boxShadow: AppConstants.softShadow,
          border: notification.isRead 
              ? null 
              : Border.all(color: AppConstants.primaryColor.withOpacity(0.2), width: 1),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Icon
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: notification.color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
              ),
              child: Icon(
                notification.icon,
                color: notification.color,
                size: 20,
              ),
            ),
            
            const SizedBox(width: AppConstants.paddingMedium),
            
            // Content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          notification.title,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: notification.isRead ? FontWeight.w500 : FontWeight.w600,
                            color: notification.isRead 
                                ? AppConstants.textPrimary 
                                : AppConstants.primaryColor,
                          ),
                        ),
                      ),
                      if (!notification.isRead)
                        Container(
                          width: 8,
                          height: 8,
                          decoration: BoxDecoration(
                            color: AppConstants.primaryColor,
                            shape: BoxShape.circle,
                          ),
                        ),
                    ],
                  ),
                  
                  const SizedBox(height: 4),
                  
                  Text(
                    notification.message,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppConstants.textSecondary,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  
                  const SizedBox(height: AppConstants.paddingSmall),
                  
                  Text(
                    _formatTimestamp(notification.timestamp),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppConstants.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            
            // More options
            IconButton(
              icon: const Icon(
                Icons.more_vert,
                color: AppConstants.textSecondary,
                size: 16,
              ),
              onPressed: () => _showNotificationOptions(notification),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.notifications_none,
            size: 64,
            color: AppConstants.textSecondary.withOpacity(0.5),
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          Text(
            'No notifications yet',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: AppConstants.textSecondary,
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            'We\'ll notify you when there\'s something new',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppConstants.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _markAsRead(NotificationItem notification) {
    setState(() {
      notification.isRead = true;
    });
  }

  void _markAllAsRead() {
    setState(() {
      for (var notification in _notifications) {
        notification.isRead = true;
      }
    });
  }

  void _showNotificationOptions(NotificationItem notification) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(AppConstants.radiusLarge),
            topRight: Radius.circular(AppConstants.radiusLarge),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle
            Container(
              margin: const EdgeInsets.only(top: AppConstants.paddingMedium),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            
            Padding(
              padding: const EdgeInsets.all(AppConstants.paddingLarge),
              child: Column(
                children: [
                  ListTile(
                    leading: Icon(
                      notification.isRead ? Icons.mark_as_unread : Icons.mark_email_read,
                    ),
                    title: Text(notification.isRead ? 'Mark as unread' : 'Mark as read'),
                    onTap: () {
                      Navigator.pop(context);
                      setState(() {
                        notification.isRead = !notification.isRead;
                      });
                    },
                  ),
                  ListTile(
                    leading: const Icon(Icons.delete, color: AppConstants.errorColor),
                    title: const Text('Delete', style: TextStyle(color: AppConstants.errorColor)),
                    onTap: () {
                      Navigator.pop(context);
                      setState(() {
                        _notifications.remove(notification);
                      });
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
    }
  }
}

enum NotificationType {
  courseUpdate,
  reminder,
  achievement,
}

class NotificationItem {
  final String id;
  final String title;
  final String message;
  final NotificationType type;
  final DateTime timestamp;
  bool isRead;
  final IconData icon;
  final Color color;

  NotificationItem({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    required this.timestamp,
    required this.isRead,
    required this.icon,
    required this.color,
  });
}
