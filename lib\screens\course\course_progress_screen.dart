import 'package:flutter/material.dart';
import '../../utils/constants.dart';
import '../../widgets/video_card.dart';
import '../../widgets/progress_card.dart';
import '../video/video_player_screen.dart';

class CourseProgressScreen extends StatefulWidget {
  final String courseId;
  final String courseTitle;

  const CourseProgressScreen({
    super.key,
    required this.courseId,
    required this.courseTitle,
  });

  @override
  State<CourseProgressScreen> createState() => _CourseProgressScreenState();
}

class _CourseProgressScreenState extends State<CourseProgressScreen> {
  final List<Milestone> _milestones = [
    Milestone(
      title: 'Getting Started',
      description: 'Complete the first 3 videos',
      isCompleted: true,
      progress: 1.0,
    ),
    Milestone(
      title: 'Basic Concepts',
      description: 'Understand fundamental concepts',
      isCompleted: true,
      progress: 1.0,
    ),
    Milestone(
      title: 'Intermediate Level',
      description: 'Apply concepts in practice',
      isCompleted: false,
      progress: 0.6,
    ),
    Miles<PERSON>(
      title: 'Advanced Topics',
      description: 'Master advanced techniques',
      isCompleted: false,
      progress: 0.0,
    ),
    Milestone(
      title: 'Final Project',
      description: 'Complete the capstone project',
      isCompleted: false,
      progress: 0.0,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    final completedMilestones = _milestones.where((m) => m.isCompleted).length;
    final overallProgress = completedMilestones / _milestones.length;

    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: AppBar(
        title: Text(widget.courseTitle),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Overall Progress Header
            Container(
              margin: const EdgeInsets.all(AppConstants.paddingLarge),
              padding: const EdgeInsets.all(AppConstants.paddingLarge),
              decoration: BoxDecoration(
                gradient: AppConstants.primaryGradient,
                borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
                boxShadow: AppConstants.mediumShadow,
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Course Progress',
                              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: AppConstants.paddingSmall),
                            Text(
                              '${(overallProgress * 100).toInt()}% Complete',
                              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                color: Colors.white.withOpacity(0.9),
                              ),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(
                        width: 80,
                        height: 80,
                        child: Stack(
                          alignment: Alignment.center,
                          children: [
                            SizedBox(
                              width: 80,
                              height: 80,
                              child: CircularProgressIndicator(
                                value: overallProgress,
                                strokeWidth: 6,
                                backgroundColor: Colors.white.withOpacity(0.3),
                                valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            ),
                            Text(
                              '${(overallProgress * 100).toInt()}%',
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: AppConstants.paddingLarge),
                  
                  // Stats Row
                  Row(
                    children: [
                      Expanded(
                        child: _buildStatItem(
                          context,
                          '15',
                          'Videos',
                          Icons.play_circle_outline,
                        ),
                      ),
                      Container(
                        width: 1,
                        height: 40,
                        color: Colors.white.withOpacity(0.3),
                      ),
                      Expanded(
                        child: _buildStatItem(
                          context,
                          '12h 30m',
                          'Duration',
                          Icons.access_time,
                        ),
                      ),
                      Container(
                        width: 1,
                        height: 40,
                        color: Colors.white.withOpacity(0.3),
                      ),
                      Expanded(
                        child: _buildStatItem(
                          context,
                          '8h 45m',
                          'Watched',
                          Icons.visibility,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            // Milestones Section
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: AppConstants.paddingLarge),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Milestones',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  
                  const SizedBox(height: AppConstants.paddingMedium),
                  
                  ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: _milestones.length,
                    itemBuilder: (context, index) {
                      final milestone = _milestones[index];
                      return Padding(
                        padding: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
                        child: _buildMilestoneCard(milestone, index),
                      );
                    },
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: AppConstants.paddingLarge),
            
            // Upcoming Videos Section
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: AppConstants.paddingLarge),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Continue Learning',
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      TextButton(
                        onPressed: () {
                          // Navigate to all course videos
                        },
                        child: const Text('View All'),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: AppConstants.paddingMedium),
                  
                  // Next Videos
                  ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: 3, // Show next 3 videos
                    itemBuilder: (context, index) {
                      return Padding(
                        padding: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
                        child: VideoCard(
                          title: 'Video ${index + 8}: Advanced Flutter Concepts',
                          duration: '15:30',
                          thumbnailUrl: 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg',
                          progress: index == 0 ? 0.3 : 0.0,
                          subtitle: 'Lesson ${index + 8}',
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const VideoPlayerScreen(
                                  videoId: 'dQw4w9WgXcQ',
                                  title: 'Advanced Flutter Concepts',
                                  description: 'Learn advanced Flutter concepts in this comprehensive tutorial.',
                                ),
                              ),
                            );
                          },
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: AppConstants.paddingLarge),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(BuildContext context, String value, String label, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          color: Colors.white.withOpacity(0.9),
          size: 20,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.white.withOpacity(0.8),
          ),
        ),
      ],
    );
  }

  Widget _buildMilestoneCard(Milestone milestone, int index) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: AppConstants.cardColor,
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        boxShadow: AppConstants.softShadow,
        border: milestone.isCompleted
            ? Border.all(color: AppConstants.successColor.withOpacity(0.3), width: 2)
            : null,
      ),
      child: Row(
        children: [
          // Milestone Number/Status
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: milestone.isCompleted
                  ? AppConstants.successColor
                  : milestone.progress > 0
                      ? AppConstants.primaryColor
                      : Colors.grey.shade300,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: milestone.isCompleted
                  ? const Icon(
                      Icons.check,
                      color: Colors.white,
                      size: 20,
                    )
                  : Text(
                      '${index + 1}',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: milestone.progress > 0 ? Colors.white : AppConstants.textSecondary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
            ),
          ),
          
          const SizedBox(width: AppConstants.paddingMedium),
          
          // Milestone Info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  milestone.title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: milestone.isCompleted
                        ? AppConstants.successColor
                        : AppConstants.textPrimary,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  milestone.description,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppConstants.textSecondary,
                  ),
                ),
                if (!milestone.isCompleted && milestone.progress > 0) ...[
                  const SizedBox(height: AppConstants.paddingSmall),
                  LinearProgressIndicator(
                    value: milestone.progress,
                    backgroundColor: AppConstants.primaryColor.withOpacity(0.1),
                    valueColor: const AlwaysStoppedAnimation<Color>(AppConstants.primaryColor),
                    borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${(milestone.progress * 100).toInt()}% complete',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppConstants.primaryColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ],
            ),
          ),
          
          // Status Icon
          if (milestone.isCompleted)
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppConstants.successColor.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.workspace_premium,
                color: AppConstants.successColor,
                size: 20,
              ),
            ),
        ],
      ),
    );
  }
}

class Milestone {
  final String title;
  final String description;
  final bool isCompleted;
  final double progress;

  Milestone({
    required this.title,
    required this.description,
    required this.isCompleted,
    required this.progress,
  });
}
