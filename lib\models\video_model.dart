class VideoModel {
  final String id;
  final String title;
  final String description;
  final String thumbnailUrl;
  final String youtubeId;
  final Duration duration;
  final String category;
  final List<String> tags;
  final DateTime uploadedAt;
  final String uploadedBy;
  final int viewCount;
  final double rating;
  final List<String> relatedVideos;
  final Map<String, dynamic> metadata;

  VideoModel({
    required this.id,
    required this.title,
    required this.description,
    required this.thumbnailUrl,
    required this.youtubeId,
    required this.duration,
    required this.category,
    this.tags = const [],
    required this.uploadedAt,
    required this.uploadedBy,
    this.viewCount = 0,
    this.rating = 0.0,
    this.relatedVideos = const [],
    this.metadata = const {},
  });

  factory VideoModel.fromJson(Map<String, dynamic> json) {
    return VideoModel(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      thumbnailUrl: json['thumbnailUrl'] ?? '',
      youtubeId: json['youtubeId'] ?? '',
      duration: Duration(seconds: json['durationSeconds'] ?? 0),
      category: json['category'] ?? '',
      tags: List<String>.from(json['tags'] ?? []),
      uploadedAt: DateTime.parse(json['uploadedAt'] ?? DateTime.now().toIso8601String()),
      uploadedBy: json['uploadedBy'] ?? '',
      viewCount: json['viewCount'] ?? 0,
      rating: (json['rating'] ?? 0.0).toDouble(),
      relatedVideos: List<String>.from(json['relatedVideos'] ?? []),
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'thumbnailUrl': thumbnailUrl,
      'youtubeId': youtubeId,
      'durationSeconds': duration.inSeconds,
      'category': category,
      'tags': tags,
      'uploadedAt': uploadedAt.toIso8601String(),
      'uploadedBy': uploadedBy,
      'viewCount': viewCount,
      'rating': rating,
      'relatedVideos': relatedVideos,
      'metadata': metadata,
    };
  }

  VideoModel copyWith({
    String? id,
    String? title,
    String? description,
    String? thumbnailUrl,
    String? youtubeId,
    Duration? duration,
    String? category,
    List<String>? tags,
    DateTime? uploadedAt,
    String? uploadedBy,
    int? viewCount,
    double? rating,
    List<String>? relatedVideos,
    Map<String, dynamic>? metadata,
  }) {
    return VideoModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      youtubeId: youtubeId ?? this.youtubeId,
      duration: duration ?? this.duration,
      category: category ?? this.category,
      tags: tags ?? this.tags,
      uploadedAt: uploadedAt ?? this.uploadedAt,
      uploadedBy: uploadedBy ?? this.uploadedBy,
      viewCount: viewCount ?? this.viewCount,
      rating: rating ?? this.rating,
      relatedVideos: relatedVideos ?? this.relatedVideos,
      metadata: metadata ?? this.metadata,
    );
  }

  // Helper methods
  String getFormattedDuration() {
    int hours = duration.inHours;
    int minutes = duration.inMinutes.remainder(60);
    int seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '${hours}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes}:${seconds.toString().padLeft(2, '0')}';
    }
  }

  String getYouTubeUrl() {
    return 'https://www.youtube.com/watch?v=$youtubeId';
  }

  String getYouTubeThumbnailUrl({String quality = 'maxresdefault'}) {
    return 'https://img.youtube.com/vi/$youtubeId/$quality.jpg';
  }

  bool hasTag(String tag) {
    return tags.contains(tag.toLowerCase());
  }

  String getFormattedViewCount() {
    if (viewCount < 1000) {
      return viewCount.toString();
    } else if (viewCount < 1000000) {
      return '${(viewCount / 1000).toStringAsFixed(1)}K';
    } else {
      return '${(viewCount / 1000000).toStringAsFixed(1)}M';
    }
  }

  String getFormattedRating() {
    return rating.toStringAsFixed(1);
  }
}

class VideoProgress {
  final String videoId;
  final String userId;
  final Duration watchedDuration;
  final Duration totalDuration;
  final bool isCompleted;
  final DateTime lastWatchedAt;
  final List<Duration> watchedSegments;

  VideoProgress({
    required this.videoId,
    required this.userId,
    required this.watchedDuration,
    required this.totalDuration,
    this.isCompleted = false,
    required this.lastWatchedAt,
    this.watchedSegments = const [],
  });

  factory VideoProgress.fromJson(Map<String, dynamic> json) {
    return VideoProgress(
      videoId: json['videoId'] ?? '',
      userId: json['userId'] ?? '',
      watchedDuration: Duration(seconds: json['watchedDurationSeconds'] ?? 0),
      totalDuration: Duration(seconds: json['totalDurationSeconds'] ?? 0),
      isCompleted: json['isCompleted'] ?? false,
      lastWatchedAt: DateTime.parse(json['lastWatchedAt'] ?? DateTime.now().toIso8601String()),
      watchedSegments: (json['watchedSegments'] as List<dynamic>?)
          ?.map((segment) => Duration(seconds: segment))
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'videoId': videoId,
      'userId': userId,
      'watchedDurationSeconds': watchedDuration.inSeconds,
      'totalDurationSeconds': totalDuration.inSeconds,
      'isCompleted': isCompleted,
      'lastWatchedAt': lastWatchedAt.toIso8601String(),
      'watchedSegments': watchedSegments.map((segment) => segment.inSeconds).toList(),
    };
  }

  double get progressPercentage {
    if (totalDuration.inSeconds == 0) return 0.0;
    return (watchedDuration.inSeconds / totalDuration.inSeconds).clamp(0.0, 1.0);
  }

  String get formattedProgress {
    return '${(progressPercentage * 100).toInt()}%';
  }
}
