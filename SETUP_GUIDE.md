# Spirit App - Complete Setup Guide

## 🚀 Quick Start

This guide will help you set up the Spirit App learning platform from scratch.

## 📋 Prerequisites

Before you begin, ensure you have the following installed:

- **Flutter SDK** (3.8.0 or higher)
- **Android Studio** or **VS Code** with Flutter extensions
- **Git** for version control
- **Google Chrome** (for web debugging, optional)

## 🔧 Step-by-Step Setup

### 1. Flutter Installation

If you haven't installed Flutter yet:

1. Download Flutter SDK from [flutter.dev](https://flutter.dev/docs/get-started/install)
2. Extract and add Flutter to your PATH
3. Run `flutter doctor` to verify installation
4. Install Android Studio and set up Android SDK

### 2. Project Setup

```bash
# Clone the repository
git clone <your-repo-url>
cd spirit

# Install dependencies
flutter pub get

# Check for any issues
flutter doctor
```

### 3. Firebase Configuration

#### 3.1 Create Firebase Project
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Click "Create a project"
3. Enter project name: `spirit-learning-app`
4. Enable Google Analytics (optional)
5. Create project

#### 3.2 Enable Authentication
1. In Firebase Console, go to **Authentication**
2. Click **Get started**
3. Go to **Sign-in method** tab
4. Enable **Email/Password**
5. Enable **Google** sign-in
   - Add your app's SHA-1 fingerprint
   - Download updated `google-services.json`

#### 3.3 Enable Firestore Database
1. Go to **Firestore Database**
2. Click **Create database**
3. Choose **Start in test mode** (for development)
4. Select a location close to your users

#### 3.4 Add Android App
1. In Project Overview, click **Add app** → **Android**
2. Enter package name: `com.spirit.app`
3. Download `google-services.json`
4. Place it in `android/app/` directory
5. Follow the configuration steps

### 4. YouTube API Setup

#### 4.1 Enable YouTube Data API
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select your Firebase project
3. Go to **APIs & Services** → **Library**
4. Search for "YouTube Data API v3"
5. Click **Enable**

#### 4.2 Create API Key
1. Go to **APIs & Services** → **Credentials**
2. Click **Create Credentials** → **API Key**
3. Copy the API key
4. (Optional) Restrict the key to YouTube Data API v3

#### 4.3 Update App Configuration
1. Open `lib/utils/constants.dart`
2. Replace `YOUR_YOUTUBE_API_KEY` with your actual API key:
   ```dart
   static const String youtubeApiKey = 'AIzaSyYour_Actual_API_Key_Here';
   ```

### 5. Android Configuration

#### 5.1 Update Package Name (if needed)
If you want to change the package name from `com.spirit.app`:

1. Update `android/app/build.gradle.kts`:
   ```kotlin
   namespace = "com.yourcompany.yourapp"
   applicationId = "com.yourcompany.yourapp"
   ```

2. Update Firebase configuration with new package name

#### 5.2 Get SHA-1 Fingerprint
```bash
# For debug keystore
keytool -list -v -keystore ~/.android/debug.keystore -alias androiddebugkey -storepass android -keypass android

# For Windows
keytool -list -v -keystore %USERPROFILE%\.android\debug.keystore -alias androiddebugkey -storepass android -keypass android
```

Add this SHA-1 to your Firebase project settings.

### 6. Running the App

```bash
# Check connected devices
flutter devices

# Run on Android device/emulator
flutter run

# Run in debug mode with hot reload
flutter run --debug

# Build for release
flutter build apk --release
```

## 🔍 Troubleshooting

### Common Issues

#### 1. Firebase Configuration Issues
- **Error**: `google-services.json not found`
- **Solution**: Ensure the file is in `android/app/` directory

#### 2. YouTube API Issues
- **Error**: API key not working
- **Solution**: Check if YouTube Data API v3 is enabled and API key is correct

#### 3. Build Issues
- **Error**: Gradle build failed
- **Solution**: 
  ```bash
  cd android
  ./gradlew clean
  cd ..
  flutter clean
  flutter pub get
  ```

#### 4. Authentication Issues
- **Error**: Google Sign-In not working
- **Solution**: Verify SHA-1 fingerprint is added to Firebase project

### Debug Commands

```bash
# Clean build cache
flutter clean

# Get dependencies
flutter pub get

# Check for issues
flutter doctor -v

# Run with verbose logging
flutter run -v

# Check connected devices
flutter devices
```

## 📱 Testing the App

### 1. Authentication Testing
1. Launch the app
2. Try email/password signup
3. Try Google Sign-In
4. Verify user data in Firebase Console

### 2. Video Functionality Testing
1. Go to Playlist tab
2. Add a YouTube URL
3. Verify video appears in playlist
4. Test video playback

### 3. Progress Tracking Testing
1. Watch a video partially
2. Check if progress is saved
3. Verify progress appears on home dashboard

## 🚀 Deployment

### Android Release Build

1. **Generate Keystore** (for production):
   ```bash
   keytool -genkey -v -keystore ~/upload-keystore.jks -keyalg RSA -keysize 2048 -validity 10000 -alias upload
   ```

2. **Configure Signing** in `android/app/build.gradle.kts`

3. **Build Release APK**:
   ```bash
   flutter build apk --release
   ```

4. **Build App Bundle** (recommended for Play Store):
   ```bash
   flutter build appbundle --release
   ```

## 📚 Additional Resources

- [Flutter Documentation](https://flutter.dev/docs)
- [Firebase Documentation](https://firebase.google.com/docs)
- [YouTube Data API Documentation](https://developers.google.com/youtube/v3)
- [Material Design Guidelines](https://material.io/design)

## 🆘 Support

If you encounter any issues:

1. Check the troubleshooting section above
2. Search existing issues in the repository
3. Create a new issue with detailed error logs
4. Contact <NAME_EMAIL>

## 🎉 You're Ready!

Once you've completed all steps, your Spirit App should be running successfully with:

- ✅ Firebase Authentication
- ✅ YouTube Video Integration
- ✅ Progress Tracking
- ✅ Modern UI/UX
- ✅ Offline Capabilities

Happy learning! 🚀
