import 'package:flutter/material.dart';
import '../../utils/constants.dart';
import '../../widgets/custom_text_field.dart';
import '../../widgets/course_card.dart';
import '../../widgets/category_chip.dart';

class CoursesScreen extends StatefulWidget {
  const CoursesScreen({super.key});

  @override
  State<CoursesScreen> createState() => _CoursesScreenState();
}

class _CoursesScreenState extends State<CoursesScreen> with TickerProviderStateMixin {
  final _searchController = TextEditingController();
  late TabController _tabController;
  String _selectedCategory = 'All';
  String _selectedFilter = 'All';
  
  final List<String> _tabs = ['All', 'Enrolled', 'Completed', 'Favorites'];
  final List<String> _filters = ['All', 'Free', 'Paid', 'Beginner', 'Intermediate', 'Advanced'];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabs.length, vsync: this);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            // App Bar
            Padding(
              padding: const EdgeInsets.all(AppConstants.paddingLarge),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        'Courses',
                        style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      IconButton(
                        icon: const Icon(Icons.filter_list),
                        onPressed: _showFilterBottomSheet,
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: AppConstants.paddingMedium),
                  
                  // Search Bar
                  SearchTextField(
                    controller: _searchController,
                    hintText: 'Search courses...',
                    onChanged: (value) {
                      // Implement search functionality
                    },
                  ),
                ],
              ),
            ),
            
            // Tabs
            Container(
              margin: const EdgeInsets.symmetric(horizontal: AppConstants.paddingLarge),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
              ),
              child: TabBar(
                controller: _tabController,
                indicator: BoxDecoration(
                  color: AppConstants.primaryColor,
                  borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
                ),
                labelColor: Colors.white,
                unselectedLabelColor: AppConstants.textSecondary,
                labelStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                unselectedLabelStyle: Theme.of(context).textTheme.bodyMedium,
                tabs: _tabs.map((tab) => Tab(text: tab)).toList(),
              ),
            ),
            
            const SizedBox(height: AppConstants.paddingMedium),
            
            // Categories
            SizedBox(
              height: 40,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                padding: const EdgeInsets.symmetric(horizontal: AppConstants.paddingLarge),
                itemCount: ['All', ...AppConstants.courseCategories].length,
                itemBuilder: (context, index) {
                  final category = ['All', ...AppConstants.courseCategories][index];
                  return Padding(
                    padding: EdgeInsets.only(
                      right: AppConstants.paddingMedium,
                    ),
                    child: CategoryChip(
                      label: category,
                      isSelected: _selectedCategory == category,
                      onTap: () {
                        setState(() {
                          _selectedCategory = category;
                        });
                      },
                    ),
                  );
                },
              ),
            ),
            
            const SizedBox(height: AppConstants.paddingMedium),
            
            // Course List
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildCourseGrid(), // All
                  _buildCourseGrid(), // Enrolled
                  _buildCourseGrid(), // Completed
                  _buildCourseGrid(), // Favorites
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCourseGrid() {
    return GridView.builder(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.75,
        crossAxisSpacing: AppConstants.paddingMedium,
        mainAxisSpacing: AppConstants.paddingMedium,
      ),
      itemCount: 10, // Replace with actual course count
      itemBuilder: (context, index) {
        return CourseCard(
          title: 'Flutter Development Course ${index + 1}',
          instructor: 'John Doe',
          duration: '12h 30m',
          rating: 4.8,
          enrollmentCount: '2.5K',
          thumbnailUrl: 'https://via.placeholder.com/250x140',
          progress: index % 3 == 0 ? 0.65 : null, // Show progress for some courses
          onTap: () {
            // Navigate to course details
          },
          onFavorite: () {
            // Toggle favorite
          },
          isFavorite: index % 4 == 0, // Some courses are favorites
        );
      },
    );
  }

  void _showFilterBottomSheet() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(AppConstants.radiusLarge),
            topRight: Radius.circular(AppConstants.radiusLarge),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle
            Container(
              margin: const EdgeInsets.only(top: AppConstants.paddingMedium),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            
            Padding(
              padding: const EdgeInsets.all(AppConstants.paddingLarge),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Filter Courses',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  
                  const SizedBox(height: AppConstants.paddingLarge),
                  
                  Text(
                    'Price',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  
                  const SizedBox(height: AppConstants.paddingMedium),
                  
                  Wrap(
                    spacing: AppConstants.paddingSmall,
                    children: ['All', 'Free', 'Paid'].map<Widget>((filter) {
                      return CustomFilterChip(
                        label: filter,
                        isSelected: _selectedFilter == filter,
                        onTap: () {
                          setState(() {
                            _selectedFilter = filter;
                          });
                        },
                      );
                    }).toList(),
                  ),
                  
                  const SizedBox(height: AppConstants.paddingLarge),
                  
                  Text(
                    'Difficulty',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  
                  const SizedBox(height: AppConstants.paddingMedium),
                  
                  Wrap(
                    spacing: AppConstants.paddingSmall,
                    children: ['Beginner', 'Intermediate', 'Advanced'].map<Widget>((difficulty) {
                      return CustomFilterChip(
                        label: difficulty,
                        isSelected: _selectedFilter == difficulty,
                        onTap: () {
                          setState(() {
                            _selectedFilter = difficulty;
                          });
                        },
                      );
                    }).toList(),
                  ),
                  
                  const SizedBox(height: AppConstants.paddingLarge),
                  
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton(
                          onPressed: () {
                            setState(() {
                              _selectedFilter = 'All';
                            });
                            Navigator.pop(context);
                          },
                          child: const Text('Clear'),
                        ),
                      ),
                      const SizedBox(width: AppConstants.paddingMedium),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () {
                            Navigator.pop(context);
                            // Apply filters
                          },
                          child: const Text('Apply'),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
