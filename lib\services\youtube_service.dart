import 'dart:convert';
import 'package:http/http.dart' as http;
import '../utils/constants.dart';
import '../models/video_model.dart';

class YouTubeService {
  static final YouTubeService _instance = YouTubeService._internal();
  factory YouTubeService() => _instance;
  YouTubeService._internal();

  static const String _baseUrl = 'https://www.googleapis.com/youtube/v3';

  // Extract video ID from YouTube URL
  String? extractVideoId(String url) {
    final regExp = RegExp(
      r'(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})',
      caseSensitive: false,
    );
    
    final match = regExp.firstMatch(url);
    return match?.group(1);
  }

  // Get video details from YouTube API
  Future<VideoModel?> getVideoDetails(String videoId) async {
    try {
      final url = Uri.parse(
        '$_baseUrl/videos?part=snippet,contentDetails,statistics&id=$videoId&key=${AppConstants.youtubeApiKey}',
      );

      final response = await http.get(url);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        
        if (data['items'] != null && data['items'].isNotEmpty) {
          final item = data['items'][0];
          return _parseVideoFromYouTubeData(item);
        }
      }
      
      return null;
    } catch (e) {
      print('Error fetching video details: $e');
      return null;
    }
  }

  // Search for videos
  Future<List<VideoModel>> searchVideos(String query, {int maxResults = 10}) async {
    try {
      final url = Uri.parse(
        '$_baseUrl/search?part=snippet&q=${Uri.encodeComponent(query)}&type=video&maxResults=$maxResults&key=${AppConstants.youtubeApiKey}',
      );

      final response = await http.get(url);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        
        if (data['items'] != null) {
          final List<VideoModel> videos = [];
          
          for (final item in data['items']) {
            final videoId = item['id']['videoId'];
            final videoDetails = await getVideoDetails(videoId);
            
            if (videoDetails != null) {
              videos.add(videoDetails);
            }
          }
          
          return videos;
        }
      }
      
      return [];
    } catch (e) {
      print('Error searching videos: $e');
      return [];
    }
  }

  // Get channel details
  Future<Map<String, dynamic>?> getChannelDetails(String channelId) async {
    try {
      final url = Uri.parse(
        '$_baseUrl/channels?part=snippet,statistics&id=$channelId&key=${AppConstants.youtubeApiKey}',
      );

      final response = await http.get(url);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        
        if (data['items'] != null && data['items'].isNotEmpty) {
          final item = data['items'][0];
          return {
            'title': item['snippet']['title'],
            'description': item['snippet']['description'],
            'thumbnailUrl': item['snippet']['thumbnails']['default']['url'],
            'subscriberCount': item['statistics']['subscriberCount'],
            'videoCount': item['statistics']['videoCount'],
          };
        }
      }
      
      return null;
    } catch (e) {
      print('Error fetching channel details: $e');
      return null;
    }
  }

  // Get playlist videos
  Future<List<VideoModel>> getPlaylistVideos(String playlistId) async {
    try {
      final url = Uri.parse(
        '$_baseUrl/playlistItems?part=snippet&playlistId=$playlistId&maxResults=50&key=${AppConstants.youtubeApiKey}',
      );

      final response = await http.get(url);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        
        if (data['items'] != null) {
          final List<VideoModel> videos = [];
          
          for (final item in data['items']) {
            final videoId = item['snippet']['resourceId']['videoId'];
            final videoDetails = await getVideoDetails(videoId);
            
            if (videoDetails != null) {
              videos.add(videoDetails);
            }
          }
          
          return videos;
        }
      }
      
      return [];
    } catch (e) {
      print('Error fetching playlist videos: $e');
      return [];
    }
  }

  // Parse video data from YouTube API response
  VideoModel _parseVideoFromYouTubeData(Map<String, dynamic> item) {
    final snippet = item['snippet'];
    final contentDetails = item['contentDetails'];
    final statistics = item['statistics'];

    // Parse duration from ISO 8601 format (PT4M13S)
    final durationString = contentDetails['duration'] ?? 'PT0S';
    final duration = _parseDuration(durationString);

    return VideoModel(
      id: item['id'],
      title: snippet['title'] ?? '',
      description: snippet['description'] ?? '',
      thumbnailUrl: snippet['thumbnails']['maxresdefault']?['url'] ?? 
                   snippet['thumbnails']['high']?['url'] ?? 
                   snippet['thumbnails']['medium']?['url'] ?? '',
      youtubeId: item['id'],
      duration: duration,
      category: snippet['categoryId'] ?? '',
      tags: List<String>.from(snippet['tags'] ?? []),
      uploadedAt: DateTime.parse(snippet['publishedAt']),
      uploadedBy: snippet['channelTitle'] ?? '',
      viewCount: int.tryParse(statistics['viewCount'] ?? '0') ?? 0,
      rating: 0.0, // YouTube API doesn't provide ratings anymore
    );
  }

  // Parse ISO 8601 duration format
  Duration _parseDuration(String duration) {
    final regex = RegExp(r'PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?');
    final match = regex.firstMatch(duration);
    
    if (match != null) {
      final hours = int.tryParse(match.group(1) ?? '0') ?? 0;
      final minutes = int.tryParse(match.group(2) ?? '0') ?? 0;
      final seconds = int.tryParse(match.group(3) ?? '0') ?? 0;
      
      return Duration(hours: hours, minutes: minutes, seconds: seconds);
    }
    
    return Duration.zero;
  }

  // Validate YouTube URL
  bool isValidYouTubeUrl(String url) {
    final videoId = extractVideoId(url);
    return videoId != null && videoId.length == 11;
  }

  // Get video thumbnail URL
  String getVideoThumbnailUrl(String videoId, {String quality = 'maxresdefault'}) {
    return 'https://img.youtube.com/vi/$videoId/$quality.jpg';
  }

  // Get video URL
  String getVideoUrl(String videoId) {
    return 'https://www.youtube.com/watch?v=$videoId';
  }

  // Get embed URL
  String getEmbedUrl(String videoId) {
    return 'https://www.youtube.com/embed/$videoId';
  }

  // Get trending videos (requires API quota)
  Future<List<VideoModel>> getTrendingVideos({String regionCode = 'US'}) async {
    try {
      final url = Uri.parse(
        '$_baseUrl/videos?part=snippet,contentDetails,statistics&chart=mostPopular&regionCode=$regionCode&maxResults=20&key=${AppConstants.youtubeApiKey}',
      );

      final response = await http.get(url);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        
        if (data['items'] != null) {
          return data['items']
              .map<VideoModel>((item) => _parseVideoFromYouTubeData(item))
              .toList();
        }
      }
      
      return [];
    } catch (e) {
      print('Error fetching trending videos: $e');
      return [];
    }
  }

  // Get related videos (Note: This endpoint was deprecated by YouTube)
  Future<List<VideoModel>> getRelatedVideos(String videoId) async {
    // Since the related videos endpoint was deprecated,
    // we can search for videos with similar tags or from the same channel
    try {
      final videoDetails = await getVideoDetails(videoId);
      if (videoDetails == null) return [];

      // Search for videos from the same channel
      final query = videoDetails.uploadedBy;
      return await searchVideos(query, maxResults: 5);
    } catch (e) {
      print('Error fetching related videos: $e');
      return [];
    }
  }
}
