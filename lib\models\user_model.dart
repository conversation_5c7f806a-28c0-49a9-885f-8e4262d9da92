class UserModel {
  final String id;
  final String email;
  final String name;
  final String? photoUrl;
  final DateTime createdAt;
  final DateTime lastLoginAt;
  final int totalWatchTime; // in minutes
  final int coursesCompleted;
  final int videosWatched;
  final List<String> enrolledCourses;
  final Map<String, double> courseProgress; // courseId -> progress (0.0 to 1.0)
  final List<String> favoriteVideos;
  final Map<String, dynamic> preferences;

  UserModel({
    required this.id,
    required this.email,
    required this.name,
    this.photoUrl,
    required this.createdAt,
    required this.lastLoginAt,
    this.totalWatchTime = 0,
    this.coursesCompleted = 0,
    this.videosWatched = 0,
    this.enrolledCourses = const [],
    this.courseProgress = const {},
    this.favoriteVideos = const [],
    this.preferences = const {},
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] ?? '',
      email: json['email'] ?? '',
      name: json['name'] ?? '',
      photoUrl: json['photoUrl'],
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      lastLoginAt: DateTime.parse(json['lastLoginAt'] ?? DateTime.now().toIso8601String()),
      totalWatchTime: json['totalWatchTime'] ?? 0,
      coursesCompleted: json['coursesCompleted'] ?? 0,
      videosWatched: json['videosWatched'] ?? 0,
      enrolledCourses: List<String>.from(json['enrolledCourses'] ?? []),
      courseProgress: Map<String, double>.from(json['courseProgress'] ?? {}),
      favoriteVideos: List<String>.from(json['favoriteVideos'] ?? []),
      preferences: Map<String, dynamic>.from(json['preferences'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'name': name,
      'photoUrl': photoUrl,
      'createdAt': createdAt.toIso8601String(),
      'lastLoginAt': lastLoginAt.toIso8601String(),
      'totalWatchTime': totalWatchTime,
      'coursesCompleted': coursesCompleted,
      'videosWatched': videosWatched,
      'enrolledCourses': enrolledCourses,
      'courseProgress': courseProgress,
      'favoriteVideos': favoriteVideos,
      'preferences': preferences,
    };
  }

  UserModel copyWith({
    String? id,
    String? email,
    String? name,
    String? photoUrl,
    DateTime? createdAt,
    DateTime? lastLoginAt,
    int? totalWatchTime,
    int? coursesCompleted,
    int? videosWatched,
    List<String>? enrolledCourses,
    Map<String, double>? courseProgress,
    List<String>? favoriteVideos,
    Map<String, dynamic>? preferences,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      photoUrl: photoUrl ?? this.photoUrl,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      totalWatchTime: totalWatchTime ?? this.totalWatchTime,
      coursesCompleted: coursesCompleted ?? this.coursesCompleted,
      videosWatched: videosWatched ?? this.videosWatched,
      enrolledCourses: enrolledCourses ?? this.enrolledCourses,
      courseProgress: courseProgress ?? this.courseProgress,
      favoriteVideos: favoriteVideos ?? this.favoriteVideos,
      preferences: preferences ?? this.preferences,
    );
  }

  // Helper methods
  double getOverallProgress() {
    if (courseProgress.isEmpty) return 0.0;
    double totalProgress = courseProgress.values.fold(0.0, (sum, progress) => sum + progress);
    return totalProgress / courseProgress.length;
  }

  bool isCourseEnrolled(String courseId) {
    return enrolledCourses.contains(courseId);
  }

  bool isVideoFavorite(String videoId) {
    return favoriteVideos.contains(videoId);
  }

  String getFormattedWatchTime() {
    if (totalWatchTime < 60) {
      return '${totalWatchTime}m';
    } else {
      int hours = totalWatchTime ~/ 60;
      int minutes = totalWatchTime % 60;
      return '${hours}h ${minutes}m';
    }
  }
}
