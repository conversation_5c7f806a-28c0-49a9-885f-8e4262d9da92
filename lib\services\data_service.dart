import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_model.dart';
import '../models/video_model.dart';
import '../models/course_model.dart';
import '../utils/constants.dart';

class DataService {
  static final DataService _instance = DataService._internal();
  factory DataService() => _instance;
  DataService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Collections
  static const String _usersCollection = 'users';
  static const String _videosCollection = 'videos';
  static const String _coursesCollection = 'courses';
  static const String _progressCollection = 'progress';
  static const String _playlistsCollection = 'playlists';

  // User Management
  Future<void> saveUserData(UserModel user) async {
    try {
      await _firestore
          .collection(_usersCollection)
          .doc(user.id)
          .set(user.toJson());
    } catch (e) {
      throw Exception('Failed to save user data: $e');
    }
  }

  Future<UserModel?> getUserData(String userId) async {
    try {
      final doc = await _firestore
          .collection(_usersCollection)
          .doc(userId)
          .get();

      if (doc.exists) {
        return UserModel.fromJson(doc.data()!);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get user data: $e');
    }
  }

  Future<void> updateUserProgress(String userId, Map<String, dynamic> progressData) async {
    try {
      await _firestore
          .collection(_usersCollection)
          .doc(userId)
          .update(progressData);
    } catch (e) {
      throw Exception('Failed to update user progress: $e');
    }
  }

  // Video Management
  Future<void> saveVideo(VideoModel video) async {
    try {
      await _firestore
          .collection(_videosCollection)
          .doc(video.id)
          .set(video.toJson());
    } catch (e) {
      throw Exception('Failed to save video: $e');
    }
  }

  Future<VideoModel?> getVideo(String videoId) async {
    try {
      final doc = await _firestore
          .collection(_videosCollection)
          .doc(videoId)
          .get();

      if (doc.exists) {
        return VideoModel.fromJson(doc.data()!);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get video: $e');
    }
  }

  Future<List<VideoModel>> getUserVideos(String userId) async {
    try {
      final querySnapshot = await _firestore
          .collection(_videosCollection)
          .where('uploadedBy', isEqualTo: userId)
          .orderBy('uploadedAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => VideoModel.fromJson(doc.data()))
          .toList();
    } catch (e) {
      throw Exception('Failed to get user videos: $e');
    }
  }

  // Video Progress Management
  Future<void> saveVideoProgress(VideoProgress progress) async {
    try {
      await _firestore
          .collection(_progressCollection)
          .doc('${progress.userId}_${progress.videoId}')
          .set(progress.toJson());
    } catch (e) {
      throw Exception('Failed to save video progress: $e');
    }
  }

  Future<VideoProgress?> getVideoProgress(String userId, String videoId) async {
    try {
      final doc = await _firestore
          .collection(_progressCollection)
          .doc('${userId}_$videoId')
          .get();

      if (doc.exists) {
        return VideoProgress.fromJson(doc.data()!);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get video progress: $e');
    }
  }

  Future<List<VideoProgress>> getUserVideoProgress(String userId) async {
    try {
      final querySnapshot = await _firestore
          .collection(_progressCollection)
          .where('userId', isEqualTo: userId)
          .get();

      return querySnapshot.docs
          .map((doc) => VideoProgress.fromJson(doc.data()))
          .toList();
    } catch (e) {
      throw Exception('Failed to get user video progress: $e');
    }
  }

  // Course Management
  Future<void> saveCourse(CourseModel course) async {
    try {
      await _firestore
          .collection(_coursesCollection)
          .doc(course.id)
          .set(course.toJson());
    } catch (e) {
      throw Exception('Failed to save course: $e');
    }
  }

  Future<CourseModel?> getCourse(String courseId) async {
    try {
      final doc = await _firestore
          .collection(_coursesCollection)
          .doc(courseId)
          .get();

      if (doc.exists) {
        return CourseModel.fromJson(doc.data()!);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get course: $e');
    }
  }

  Future<List<CourseModel>> getAllCourses() async {
    try {
      final querySnapshot = await _firestore
          .collection(_coursesCollection)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => CourseModel.fromJson(doc.data()))
          .toList();
    } catch (e) {
      throw Exception('Failed to get courses: $e');
    }
  }

  Future<List<CourseModel>> getCoursesByCategory(String category) async {
    try {
      final querySnapshot = await _firestore
          .collection(_coursesCollection)
          .where('category', isEqualTo: category)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => CourseModel.fromJson(doc.data()))
          .toList();
    } catch (e) {
      throw Exception('Failed to get courses by category: $e');
    }
  }

  // Playlist Management
  Future<void> addVideoToPlaylist(String userId, String videoId) async {
    try {
      await _firestore
          .collection(_playlistsCollection)
          .doc(userId)
          .update({
        'videoIds': FieldValue.arrayUnion([videoId]),
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      // If document doesn't exist, create it
      await _firestore
          .collection(_playlistsCollection)
          .doc(userId)
          .set({
        'userId': userId,
        'videoIds': [videoId],
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      });
    }
  }

  Future<void> removeVideoFromPlaylist(String userId, String videoId) async {
    try {
      await _firestore
          .collection(_playlistsCollection)
          .doc(userId)
          .update({
        'videoIds': FieldValue.arrayRemove([videoId]),
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      throw Exception('Failed to remove video from playlist: $e');
    }
  }

  Future<List<String>> getUserPlaylist(String userId) async {
    try {
      final doc = await _firestore
          .collection(_playlistsCollection)
          .doc(userId)
          .get();

      if (doc.exists) {
        final data = doc.data()!;
        return List<String>.from(data['videoIds'] ?? []);
      }
      return [];
    } catch (e) {
      throw Exception('Failed to get user playlist: $e');
    }
  }

  // Local Storage (SharedPreferences)
  Future<void> saveToLocal(String key, String value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(key, value);
  }

  Future<String?> getFromLocal(String key) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(key);
  }

  Future<void> saveBoolToLocal(String key, bool value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(key, value);
  }

  Future<bool> getBoolFromLocal(String key, {bool defaultValue = false}) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(key) ?? defaultValue;
  }

  Future<void> removeFromLocal(String key) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(key);
  }

  Future<void> clearLocalStorage() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.clear();
  }

  // Analytics and Statistics
  Future<Map<String, dynamic>> getUserStats(String userId) async {
    try {
      final progressList = await getUserVideoProgress(userId);
      final completedVideos = progressList.where((p) => p.isCompleted).length;
      final totalWatchTime = progressList.fold<Duration>(
        Duration.zero,
        (total, progress) => total + progress.watchedDuration,
      );

      return {
        'totalVideos': progressList.length,
        'completedVideos': completedVideos,
        'totalWatchTime': totalWatchTime.inMinutes,
        'completionRate': progressList.isNotEmpty 
            ? (completedVideos / progressList.length * 100).round()
            : 0,
      };
    } catch (e) {
      throw Exception('Failed to get user stats: $e');
    }
  }

  // Search functionality
  Future<List<VideoModel>> searchVideos(String query) async {
    try {
      final querySnapshot = await _firestore
          .collection(_videosCollection)
          .where('title', isGreaterThanOrEqualTo: query)
          .where('title', isLessThanOrEqualTo: '$query\uf8ff')
          .limit(20)
          .get();

      return querySnapshot.docs
          .map((doc) => VideoModel.fromJson(doc.data()))
          .toList();
    } catch (e) {
      throw Exception('Failed to search videos: $e');
    }
  }

  Future<List<CourseModel>> searchCourses(String query) async {
    try {
      final querySnapshot = await _firestore
          .collection(_coursesCollection)
          .where('title', isGreaterThanOrEqualTo: query)
          .where('title', isLessThanOrEqualTo: '$query\uf8ff')
          .limit(20)
          .get();

      return querySnapshot.docs
          .map((doc) => CourseModel.fromJson(doc.data()))
          .toList();
    } catch (e) {
      throw Exception('Failed to search courses: $e');
    }
  }
}
