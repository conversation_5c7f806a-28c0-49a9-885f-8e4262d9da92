import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../utils/constants.dart';

class VideoCard extends StatelessWidget {
  final String title;
  final String duration;
  final String thumbnailUrl;
  final double progress;
  final bool isWatched;
  final VoidCallback? onTap;
  final VoidCallback? onMoreOptions;
  final String? subtitle;
  final String? uploadDate;

  const VideoCard({
    super.key,
    required this.title,
    required this.duration,
    required this.thumbnailUrl,
    this.progress = 0.0,
    this.isWatched = false,
    this.onTap,
    this.onMoreOptions,
    this.subtitle,
    this.uploadDate,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        decoration: BoxDecoration(
          color: AppConstants.cardColor,
          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
          boxShadow: AppConstants.softShadow,
        ),
        child: Row(
          children: [
            // Thumbnail
            Stack(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
                  child: CachedNetworkImage(
                    imageUrl: thumbnailUrl,
                    height: 80,
                    width: 120,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Container(
                      height: 80,
                      width: 120,
                      color: Colors.grey.shade200,
                      child: const Center(
                        child: CircularProgressIndicator(),
                      ),
                    ),
                    errorWidget: (context, url, error) => Container(
                      height: 80,
                      width: 120,
                      color: Colors.grey.shade200,
                      child: const Icon(
                        Icons.video_library,
                        color: AppConstants.textSecondary,
                      ),
                    ),
                  ),
                ),
                
                // Play Button Overlay
                Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
                    ),
                    child: const Center(
                      child: Icon(
                        Icons.play_arrow,
                        color: Colors.white,
                        size: 32,
                      ),
                    ),
                  ),
                ),
                
                // Duration Badge
                Positioned(
                  bottom: 4,
                  right: 4,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 6,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.8),
                      borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
                    ),
                    child: Text(
                      duration,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
                
                // Watched Indicator
                if (isWatched)
                  Positioned(
                    top: 4,
                    left: 4,
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color: AppConstants.successColor,
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.check,
                        color: Colors.white,
                        size: 12,
                      ),
                    ),
                  ),
              ],
            ),
            
            const SizedBox(width: AppConstants.paddingMedium),
            
            // Content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title
                  Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: isWatched 
                          ? AppConstants.textSecondary 
                          : AppConstants.textPrimary,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  
                  if (subtitle != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      subtitle!,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppConstants.textSecondary,
                      ),
                    ),
                  ],
                  
                  if (uploadDate != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      uploadDate!,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppConstants.textSecondary,
                      ),
                    ),
                  ],
                  
                  const SizedBox(height: AppConstants.paddingSmall),
                  
                  // Progress Bar
                  if (progress > 0 && !isWatched) ...[
                    LinearProgressIndicator(
                      value: progress,
                      backgroundColor: AppConstants.primaryColor.withOpacity(0.1),
                      valueColor: const AlwaysStoppedAnimation<Color>(AppConstants.primaryColor),
                      borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${(progress * 100).toInt()}% watched',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppConstants.primaryColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ] else if (isWatched) ...[
                    Row(
                      children: [
                        Icon(
                          Icons.check_circle,
                          color: AppConstants.successColor,
                          size: 16,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'Completed',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppConstants.successColor,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            ),
            
            // More Options Button
            if (onMoreOptions != null)
              IconButton(
                icon: const Icon(
                  Icons.more_vert,
                  color: AppConstants.textSecondary,
                ),
                onPressed: onMoreOptions,
              ),
          ],
        ),
      ),
    );
  }
}

class CompactVideoCard extends StatelessWidget {
  final String title;
  final String duration;
  final String thumbnailUrl;
  final double progress;
  final bool isWatched;
  final VoidCallback? onTap;

  const CompactVideoCard({
    super.key,
    required this.title,
    required this.duration,
    required this.thumbnailUrl,
    this.progress = 0.0,
    this.isWatched = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(AppConstants.paddingSmall),
        decoration: BoxDecoration(
          color: AppConstants.cardColor,
          borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
          border: Border.all(
            color: Colors.grey.shade200,
            width: 1,
          ),
        ),
        child: Row(
          children: [
            // Thumbnail
            Stack(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(4),
                  child: CachedNetworkImage(
                    imageUrl: thumbnailUrl,
                    height: 50,
                    width: 80,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Container(
                      height: 50,
                      width: 80,
                      color: Colors.grey.shade200,
                    ),
                    errorWidget: (context, url, error) => Container(
                      height: 50,
                      width: 80,
                      color: Colors.grey.shade200,
                      child: const Icon(
                        Icons.video_library,
                        color: AppConstants.textSecondary,
                        size: 16,
                      ),
                    ),
                  ),
                ),
                
                // Duration Badge
                Positioned(
                  bottom: 2,
                  right: 2,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 4,
                      vertical: 1,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.8),
                      borderRadius: BorderRadius.circular(2),
                    ),
                    child: Text(
                      duration,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.white,
                        fontSize: 8,
                      ),
                    ),
                  ),
                ),
                
                // Watched Indicator
                if (isWatched)
                  Positioned(
                    top: 2,
                    left: 2,
                    child: Container(
                      padding: const EdgeInsets.all(2),
                      decoration: BoxDecoration(
                        color: AppConstants.successColor,
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.check,
                        color: Colors.white,
                        size: 8,
                      ),
                    ),
                  ),
              ],
            ),
            
            const SizedBox(width: AppConstants.paddingSmall),
            
            // Content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                      color: isWatched 
                          ? AppConstants.textSecondary 
                          : AppConstants.textPrimary,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  
                  if (progress > 0 && !isWatched) ...[
                    const SizedBox(height: 4),
                    LinearProgressIndicator(
                      value: progress,
                      backgroundColor: AppConstants.primaryColor.withOpacity(0.1),
                      valueColor: const AlwaysStoppedAnimation<Color>(AppConstants.primaryColor),
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
